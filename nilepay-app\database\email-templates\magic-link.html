<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Nile Pay Magic Link</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #059669;
            margin-bottom: 10px;
        }
        .flag {
            font-size: 24px;
            margin-left: 10px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #6b7280;
            font-size: 16px;
            margin-bottom: 30px;
        }
        .magic-button {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }
        .magic-button:hover {
            transform: translateY(-2px);
        }
        .magic-info {
            background: #f0f9ff;
            border: 1px solid #7dd3fc;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .magic-info h4 {
            color: #0369a1;
            margin: 0 0 8px 0;
            font-size: 16px;
        }
        .magic-info p {
            color: #0c4a6e;
            margin: 0;
            font-size: 14px;
        }
        .security-note {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-note h4 {
            color: #92400e;
            margin: 0 0 8px 0;
            font-size: 14px;
        }
        .security-note p {
            color: #92400e;
            margin: 0;
            font-size: 14px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .ethiopian-pattern {
            background: linear-gradient(45deg, #fbbf24 25%, transparent 25%), 
                        linear-gradient(-45deg, #fbbf24 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #8b5cf6 75%), 
                        linear-gradient(-45deg, transparent 75%, #8b5cf6 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            height: 4px;
            margin: 20px 0;
        }
        .magic-icon {
            font-size: 48px;
            text-align: center;
            margin: 20px 0;
        }
        .steps {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 12px 0;
            padding: 8px 0;
        }
        .step-number {
            background: #8b5cf6;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }
        .step-text {
            color: #374151;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                Nile Pay <span class="flag">🇪🇹</span>
            </div>
            <div class="ethiopian-pattern"></div>
        </div>

        <div class="magic-icon">✨</div>
        
        <h1 class="title">Your Magic Link is Ready!</h1>
        <p class="subtitle">Secure, passwordless access to your Nile Pay account</p>

        <p>Hello!</p>
        
        <p>You requested a magic link to sign in to your Nile Pay account. Click the button below to access your account instantly - no password required!</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ .ConfirmationURL }}" class="magic-button">
                ✨ Sign In with Magic Link
            </a>
        </div>

        <div class="magic-info">
            <h4>🪄 What is a Magic Link?</h4>
            <p>A magic link is a secure, one-time login method that eliminates the need to remember passwords. It's faster, more secure, and designed for your convenience.</p>
        </div>

        <div class="steps">
            <h4 style="color: #1f2937; margin: 0 0 16px 0;">How it works:</h4>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">Click the magic link button above</div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">You'll be automatically signed in to your Nile Pay account</div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">Access all your banking features immediately</div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-text">The link expires after use for your security</div>
            </div>
        </div>

        <div class="security-note">
            <h4>🔐 Security Information</h4>
            <p><strong>This magic link will expire in 15 minutes</strong> and can only be used once. If you didn't request this link, please ignore this email and your account will remain secure.</p>
        </div>

        <p><strong>Benefits of Magic Links:</strong></p>
        <ul style="color: #374151;">
            <li>🚀 <strong>Faster login</strong> - No need to type passwords</li>
            <li>🔒 <strong>More secure</strong> - Each link is unique and expires quickly</li>
            <li>📱 <strong>Mobile-friendly</strong> - Perfect for accessing Nile Pay on any device</li>
            <li>🧠 <strong>No memory required</strong> - Never forget your password again</li>
        </ul>

        <p><strong>Troubleshooting:</strong></p>
        <ul style="color: #374151; font-size: 14px;">
            <li>If the button doesn't work, copy and paste the link below into your browser</li>
            <li>Make sure you're using the same device and browser where you requested the link</li>
            <li>Check your spam folder if you don't see this email</li>
            <li>Request a new magic link if this one has expired</li>
        </ul>

        <p>If the button above doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #8b5cf6; font-family: monospace; background: #f3f4f6; padding: 8px; border-radius: 4px;">{{ .ConfirmationURL }}</p>

        <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; padding: 16px; margin: 20px 0;">
            <h4 style="color: #166534; margin: 0 0 8px 0;">💡 Pro Tip</h4>
            <p style="color: #166534; margin: 0; font-size: 14px;">For even faster access, bookmark your Nile Pay dashboard after signing in. You can also enable biometric authentication in your account settings for the ultimate convenience!</p>
        </div>

        <div class="footer">
            <p><strong>Nile Pay Security Team</strong></p>
            <p>🇪🇹 Securing Ethiopian Digital Payments</p>
            <p style="margin-top: 16px;">
                Need help? Contact our support team at 
                <a href="mailto:<EMAIL>" style="color: #8b5cf6;"><EMAIL></a>
            </p>
            <p style="font-size: 12px; margin-top: 16px;">
                This magic link was sent to {{ .Email }} from your Nile Pay account. 
                If you didn't request this link, please ignore this email.
            </p>
        </div>
    </div>
</body>
</html>
