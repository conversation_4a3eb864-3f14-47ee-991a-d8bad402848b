self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"0bdad7db4f3baa5203038b7addba15539d780745\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"27e840ad680ef6f4d447ec059e57479285ec9984\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"29aa2c40f92545f039b9a4f2a4c5185a06941d06\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"2b3e6207b297fe030bf1bcc78c424bcbee680c6e\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"4fe50c7065c1ab376c3362b0ec6275d1781a3e01\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"67ebfe99c550a03b2e6e73cb50dcb3122a68a0a0\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"7e599f0130a9a6f749ba0b6f7f513aca53d562bc\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"9116832112ba2af2581125916a207d60e2966357\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"94026c7afb9a703ce62e293e03d2d063c1cee70e\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"c4beda5c97019931d0b65984558f38860404cdc3\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"c7b15f8559c4e26d2a71c55268ab47c8b3a527da\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"d91c2af4f1faec209d5cb8702bc2e02079964677\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    },\n    \"e2df131f9e7746aabd758f8504dc421979173136\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/(auth)/sign-up/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22createDemoUser%22%2C%22%24%24ACTION_0%22%2C%22getLoggedInUser%22%2C%22%24%24ACTION_5%22%2C%22updateUserProfile%22%2C%22signIn%22%2C%22%24%24ACTION_3%22%2C%22%24%24ACTION_4%22%2C%22%24%24ACTION_1%22%2C%22getUserInfo%22%2C%22logoutAccount%22%2C%22%24%24ACTION_2%22%2C%22signUp%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/page\": \"action-browser\",\n        \"app/(auth)/sign-up/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"