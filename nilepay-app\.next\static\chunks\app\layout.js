/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Cnext%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22SpeedInsights%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22IBM_Plex_Serif%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ibm-plex-serif%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22ibmPlexSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Cnext%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22SpeedInsights%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22IBM_Plex_Serif%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ibm-plex-serif%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22ibmPlexSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@vercel/analytics/dist/react/index.mjs */ \"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@vercel/speed-insights/dist/next/index.mjs */ \"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"IBM_Plex_Serif\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"],\"variable\":\"--font-ibm-plex-serif\"}],\"variableName\":\"ibmPlexSerif\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"IBM_Plex_Serif\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-ibm-plex-serif\\\"}],\\\"variableName\\\":\\\"ibmPlexSerif\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Cnext%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22SpeedInsights%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22IBM_Plex_Serif%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ibm-plex-serif%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22ibmPlexSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzPzBiN2UiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb25cIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/build/polyfills/process.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nvar _global_process, _global_process1;\nmodule.exports = ((_global_process = __webpack_require__.g.process) == null ? void 0 : _global_process.env) && typeof ((_global_process1 = __webpack_require__.g.process) == null ? void 0 : _global_process1.env) === \"object\" ? __webpack_require__.g.process : __webpack_require__(/*! next/dist/compiled/process */ \"(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\");\n\n//# sourceMappingURL=process.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLHFDQUFxQyxxQkFBTSxpRkFBaUYscUJBQU0sa0VBQWtFLHFCQUFNLFdBQVcsbUJBQU8sQ0FBQyw0R0FBNEI7O0FBRXpQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanM/OGY0MyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfZ2xvYmFsX3Byb2Nlc3MsIF9nbG9iYWxfcHJvY2VzczE7XG5tb2R1bGUuZXhwb3J0cyA9ICgoX2dsb2JhbF9wcm9jZXNzID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MuZW52KSAmJiB0eXBlb2YgKChfZ2xvYmFsX3Byb2Nlc3MxID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MxLmVudikgPT09IFwib2JqZWN0XCIgPyBnbG9iYWwucHJvY2VzcyA6IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcHJvY2Vzc1wiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHJvY2Vzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={592:(e,r,t)=>{var n=t(722);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},722:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(592);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/process/browser.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(function(){var e={229:function(e){var t=e.exports={};var r;var n;function defaultSetTimout(){throw new Error(\"setTimeout has not been defined\")}function defaultClearTimeout(){throw new Error(\"clearTimeout has not been defined\")}(function(){try{if(typeof setTimeout===\"function\"){r=setTimeout}else{r=defaultSetTimout}}catch(e){r=defaultSetTimout}try{if(typeof clearTimeout===\"function\"){n=clearTimeout}else{n=defaultClearTimeout}}catch(e){n=defaultClearTimeout}})();function runTimeout(e){if(r===setTimeout){return setTimeout(e,0)}if((r===defaultSetTimout||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function runClearTimeout(e){if(n===clearTimeout){return clearTimeout(e)}if((n===defaultClearTimeout||!n)&&clearTimeout){n=clearTimeout;return clearTimeout(e)}try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}var i=[];var o=false;var u;var a=-1;function cleanUpNextTick(){if(!o||!u){return}o=false;if(u.length){i=u.concat(i)}else{a=-1}if(i.length){drainQueue()}}function drainQueue(){if(o){return}var e=runTimeout(cleanUpNextTick);o=true;var t=i.length;while(t){u=i;i=[];while(++a<t){if(u){u[a].run()}}a=-1;t=i.length}u=null;o=false;runClearTimeout(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}i.push(new Item(e,t));if(i.length===1&&!o){runTimeout(drainQueue)}};function Item(e,t){this.fun=e;this.array=t}Item.prototype.run=function(){this.fun.apply(null,this.array)};t.title=\"browser\";t.browser=true;t.env={};t.argv=[];t.version=\"\";t.versions={};function noop(){}t.on=noop;t.addListener=noop;t.once=noop;t.off=noop;t.removeListener=noop;t.removeAllListeners=noop;t.emit=noop;t.prependListener=noop;t.prependOnceListener=noop;t.listeners=function(e){return[]};t.binding=function(e){throw new Error(\"process.binding is not supported\")};t.cwd=function(){return\"/\"};t.chdir=function(e){throw new Error(\"process.chdir is not supported\")};t.umask=function(){return 0}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var i=t[r]={exports:{}};var o=true;try{e[r](i,i.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r=__nccwpck_require__(229);module.exports=r})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"ec0e5b26db74\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzP2NmNjQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlYzBlNWIyNmRiNzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"IBM_Plex_Serif\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"],\"variable\":\"--font-ibm-plex-serif\"}],\"variableName\":\"ibmPlexSerif\"}":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"IBM_Plex_Serif","arguments":[{"subsets":["latin"],"weight":["400","700"],"variable":"--font-ibm-plex-serif"}],"variableName":"ibmPlexSerif"} ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__IBM_Plex_Serif_9aaaf5', '__IBM_Plex_Serif_Fallback_9aaaf5'\",\"fontStyle\":\"normal\"},\"className\":\"__className_9aaaf5\",\"variable\":\"__variable_9aaaf5\"};\n    if(true) {\n      // 1751305233743\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"IBM_Plex_Serif\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"700\"],\"variable\":\"--font-ibm-plex-serif\"}],\"variableName\":\"ibmPlexSerif\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"variable":"--font-inter"}],"variableName":"inter"} ***!
  \***********************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\",\"variable\":\"__variable_e8ce0c\"};\n    if(true) {\n      // 1751305233740\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: function() { return /* binding */ Analytics; },\n/* harmony export */   track: function() { return /* binding */ track; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,track auto */ var _s = $RefreshSig$();\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/analytics\";\nvar version = \"1.5.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.va) return;\n    window.va = function a() {\n        for(var _len = arguments.length, params = new Array(_len), _key = 0; _key < _len; _key++){\n            params[_key] = arguments[_key];\n        }\n        (window.vaq = window.vaq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return typeof window !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction setMode() {\n    let mode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"auto\";\n    if (mode === \"auto\") {\n        window.vam = detectEnvironment();\n        return;\n    }\n    window.vam = mode;\n}\nfunction getMode() {\n    const mode = isBrowser() ? window.vam : detectEnvironment();\n    return mode || \"production\";\n}\nfunction isProduction() {\n    return getMode() === \"production\";\n}\nfunction isDevelopment() {\n    return getMode() === \"development\";\n}\nfunction removeKey(key, param) {\n    let { [key]: _, ...rest } = param;\n    return rest;\n}\nfunction parseProperties(properties, options) {\n    if (!properties) return void 0;\n    let props = properties;\n    const errorProperties = [];\n    for (const [key, value] of Object.entries(properties)){\n        if (typeof value === \"object\" && value !== null) {\n            if (options.strip) {\n                props = removeKey(key, props);\n            } else {\n                errorProperties.push(key);\n            }\n        }\n    }\n    if (errorProperties.length > 0 && !options.strip) {\n        throw Error(\"The following properties are not valid: \".concat(errorProperties.join(\", \"), \". Only strings, numbers, booleans, and null are allowed.\"));\n    }\n    return props;\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/script.debug.js\";\n    }\n    if (props.basePath) {\n        return \"\".concat(props.basePath, \"/insights/script.js\");\n    }\n    return \"/_vercel/insights/script.js\";\n}\n// src/generic.ts\nfunction inject() {\n    let props = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {\n        debug: true\n    };\n    var _a;\n    if (!isBrowser()) return;\n    setMode(props.mode);\n    initQueue();\n    if (props.beforeSend) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const src = getScriptSrc(props);\n    if (document.head.querySelector('script[src*=\"'.concat(src, '\"]'))) return;\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? \"/\".concat(props.framework) : \"\");\n    script.dataset.sdkv = version;\n    if (props.disableAutoTrack) {\n        script.dataset.disableAutoTrack = \"1\";\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = \"\".concat(props.basePath, \"/insights\");\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    script.onerror = ()=>{\n        const errorMessage = isDevelopment() ? \"Please check if any ad blockers are enabled and try again.\" : \"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.\";\n        console.log(\"[Vercel Web Analytics] Failed to load script from \".concat(src, \". \").concat(errorMessage));\n    };\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    document.head.appendChild(script);\n}\nfunction track(name2, properties, options) {\n    var _a, _b;\n    if (!isBrowser()) {\n        const msg = \"[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment\";\n        if (isProduction()) {\n            console.warn(msg);\n        } else {\n            throw new Error(msg);\n        }\n        return;\n    }\n    if (!properties) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"event\", {\n            name: name2,\n            options\n        });\n        return;\n    }\n    try {\n        const props = parseProperties(properties, {\n            strip: isProduction()\n        });\n        (_b = window.va) == null ? void 0 : _b.call(window, \"event\", {\n            name: name2,\n            data: props,\n            options\n        });\n    } catch (err) {\n        if (err instanceof Error && isDevelopment()) {\n            console.error(err);\n        }\n    }\n}\nfunction pageview(param) {\n    let { route, path } = param;\n    var _a;\n    (_a = window.va) == null ? void 0 : _a.call(window, \"pageview\", {\n        route,\n        path\n    });\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction Analytics(props) {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _props_basePath;\n        inject({\n            framework: props.framework || \"react\",\n            basePath: (_props_basePath = props.basePath) !== null && _props_basePath !== void 0 ? _props_basePath : getBasePath(),\n            ...props.route !== void 0 && {\n                disableAutoTrack: true\n            },\n            ...props\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (props.route && props.path) {\n            pageview({\n                route: props.route,\n                path: props.path\n            });\n        }\n    }, [\n        props.route,\n        props.path\n    ]);\n    return null;\n}\n_s(Analytics, \"ewq31d5h+FnD/qJW52w5UqeNMN4=\");\n_c = Analytics;\n //# sourceMappingURL=index.mjs.map\nvar _c;\n$RefreshReg$(_c, \"Analytics\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeedInsights: function() { return /* binding */ SpeedInsights2; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ SpeedInsights auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n// src/nextjs/index.tsx\n\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/speed-insights\";\nvar version = \"1.2.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.si) return;\n    window.si = function a() {\n        for(var _len = arguments.length, params = new Array(_len), _key = 0; _key < _len; _key++){\n            params[_key] = arguments[_key];\n        }\n        (window.siq = window.siq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return typeof window !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction isDevelopment() {\n    return detectEnvironment() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, \"/[\".concat(key, \"]\"));\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, \"/[...\".concat(key, \"]\"));\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(\"/\".concat(escapeRegExp(value), \"(?=[/?#]|$)\"));\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js\";\n    }\n    if (props.dsn) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.js\";\n    }\n    if (props.basePath) {\n        return \"\".concat(props.basePath, \"/speed-insights/script.js\");\n    }\n    return \"/_vercel/speed-insights/script.js\";\n}\n// src/generic.ts\nfunction injectSpeedInsights() {\n    let props = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _a;\n    if (!isBrowser() || props.route === null) return null;\n    initQueue();\n    const src = getScriptSrc(props);\n    if (document.head.querySelector('script[src*=\"'.concat(src, '\"]'))) return null;\n    if (props.beforeSend) {\n        (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? \"/\".concat(props.framework) : \"\");\n    script.dataset.sdkv = version;\n    if (props.sampleRate) {\n        script.dataset.sampleRate = props.sampleRate.toString();\n    }\n    if (props.route) {\n        script.dataset.route = props.route;\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = \"\".concat(props.basePath, \"/speed-insights/vitals\");\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    script.onerror = ()=>{\n        console.log(\"[Vercel Speed Insights] Failed to load script from \".concat(src, \". Please check if any content blockers are enabled and try again.\"));\n    };\n    document.head.appendChild(script);\n    return {\n        setRoute: (route)=>{\n            script.dataset.route = route !== null && route !== void 0 ? route : void 0;\n        }\n    };\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction SpeedInsights(props) {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    const setScriptRoute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!setScriptRoute.current) {\n            var _props_framework, _props_basePath;\n            const script = injectSpeedInsights({\n                framework: (_props_framework = props.framework) !== null && _props_framework !== void 0 ? _props_framework : \"react\",\n                basePath: (_props_basePath = props.basePath) !== null && _props_basePath !== void 0 ? _props_basePath : getBasePath(),\n                ...props\n            });\n            if (script) {\n                setScriptRoute.current = script.setRoute;\n            }\n        } else if (props.route) {\n            setScriptRoute.current(props.route);\n        }\n    }, [\n        props.route\n    ]);\n    return null;\n}\n_s(SpeedInsights, \"E0RdZlqeWq0rUoAi6s7Wz4n3yAk=\");\n_c = SpeedInsights;\n// src/nextjs/utils.ts\n\nvar useRoute = ()=>{\n    _s1();\n    const params = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)() || new URLSearchParams();\n    const path = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    if (!params) {\n        return null;\n    }\n    const finalParams = Object.keys(params).length ? params : Object.fromEntries(searchParams.entries());\n    return computeRoute(path, finalParams);\n};\n_s1(useRoute, \"mDKlvUlHSNsLdotFSQDDcGxWeHA=\", false, function() {\n    return [\n        next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams,\n        next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams,\n        next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname\n    ];\n});\nfunction getBasePath2() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/nextjs/index.tsx\nfunction SpeedInsightsComponent(props) {\n    _s2();\n    const route = useRoute();\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsights, {\n        route,\n        ...props,\n        framework: \"next\",\n        basePath: getBasePath2()\n    });\n}\n_s2(SpeedInsightsComponent, \"3KQ3WaInJmQ2vo1ynSXcz4K+Qew=\", false, function() {\n    return [\n        useRoute\n    ];\n});\n_c1 = SpeedInsightsComponent;\nfunction SpeedInsights2(props) {\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n        fallback: null\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsightsComponent, {\n        ...props\n    }));\n}\n_c2 = SpeedInsights2;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"SpeedInsights\");\n$RefreshReg$(_c1, \"SpeedInsightsComponent\");\n$RefreshReg$(_c2, \"SpeedInsights2\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5C%40vercel%5C%5Canalytics%5C%5Cdist%5C%5Creact%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5C%40vercel%5C%5Cspeed-insights%5C%5Cdist%5C%5Cnext%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22SpeedInsights%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22IBM_Plex_Serif%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-ibm-plex-serif%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22ibmPlexSerif%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CKraimatic%5C%5CDesktop%5C%5Cbanking%5C%5Cnilepay-app%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);