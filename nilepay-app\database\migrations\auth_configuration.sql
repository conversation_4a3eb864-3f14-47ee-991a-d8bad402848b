-- =============================================================================
-- NILEPAY AUTHENTICATION CONFIGURATION FOR SUPABASE
-- Ethiopian Banking Application - Auth Setup
-- =============================================================================

-- =============================================================================
-- 1. AUTH SCHEMA EXTENSIONS
-- =============================================================================

-- Enable necessary extensions for auth
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================================================
-- 2. AUTH USER METADATA SETUP
-- =============================================================================

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_region TEXT;
    account_number TEXT;
BEGIN
    -- Generate random Ethiopian region
    user_region := (ARRAY['Addis Ababa', 'Oromia', 'Amhara', 'Tigray', 'SNNP', 'Somali', 'Afar', 'Benishangul-Gumuz', 'Gambela', 'Harari', 'Dire Dawa', 'Sidama'])[floor(random() * 12 + 1)];
    
    -- Insert user profile into public.users table
    INSERT INTO public.users (
        id,
        email,
        first_name,
        last_name,
        phone,
        region,
        preferred_language,
        is_verified,
        verification_status,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'firstName', 'User'),
        COALESCE(NEW.raw_user_meta_data->>'last_name', NEW.raw_user_meta_data->>'lastName', ''),
        NEW.raw_user_meta_data->>'phone',
        COALESCE(NEW.raw_user_meta_data->>'region', user_region),
        COALESCE(NEW.raw_user_meta_data->>'preferred_language', 'en'),
        NEW.email_confirmed_at IS NOT NULL,
        CASE 
            WHEN NEW.email_confirmed_at IS NOT NULL THEN 'verified'
            ELSE 'pending'
        END,
        NOW(),
        NOW()
    );

    -- Create default user settings
    INSERT INTO public.user_settings (
        user_id,
        email_notifications,
        sms_notifications,
        push_notifications,
        transaction_alerts,
        security_alerts,
        two_factor_enabled,
        theme,
        language,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        true,
        true,
        true,
        true,
        true,
        false,
        'system',
        COALESCE(NEW.raw_user_meta_data->>'preferred_language', 'en'),
        NOW(),
        NOW()
    );

    -- Generate account number for default wallet
    account_number := 'NP' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
    
    -- Create default Nile Pay wallet account
    INSERT INTO public.bank_accounts (
        user_id,
        bank_name,
        account_name,
        account_number,
        account_type,
        balance,
        available_balance,
        currency,
        is_primary,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        'Nile Pay Wallet',
        COALESCE(NEW.raw_user_meta_data->>'first_name', 'User') || ' - Default Wallet',
        account_number,
        'wallet',
        50000.00, -- Demo starting balance
        50000.00,
        'ETB',
        true,
        true,
        NOW(),
        NOW()
    );

    -- Create welcome notification
    INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        icon,
        color,
        is_read,
        created_at
    ) VALUES (
        NEW.id,
        'system',
        'Welcome to Nile Pay! 🇪🇹',
        'Your Ethiopian digital payment account has been created successfully. Start exploring secure banking features.',
        '🎉',
        'green',
        false,
        NOW()
    );

    -- Log user registration
    INSERT INTO public.audit_logs (
        user_id,
        action,
        resource_type,
        resource_id,
        new_values,
        created_at
    ) VALUES (
        NEW.id,
        'USER_REGISTERED',
        'user',
        NEW.id::TEXT,
        jsonb_build_object(
            'email', NEW.email,
            'registration_method', 'email_password',
            'region', user_region
        ),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- 3. AUTH TRIGGERS
-- =============================================================================

-- Trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to handle user email confirmation
CREATE OR REPLACE FUNCTION public.handle_user_email_confirmed()
RETURNS TRIGGER AS $$
BEGIN
    -- Update user verification status when email is confirmed
    IF OLD.email_confirmed_at IS NULL AND NEW.email_confirmed_at IS NOT NULL THEN
        UPDATE public.users 
        SET 
            is_verified = true,
            verification_status = 'verified',
            updated_at = NOW()
        WHERE id = NEW.id;

        -- Create email confirmation notification
        INSERT INTO public.notifications (
            user_id,
            type,
            title,
            message,
            icon,
            color,
            is_read,
            created_at
        ) VALUES (
            NEW.id,
            'security',
            'Email Verified Successfully ✅',
            'Your email address has been verified. You now have full access to all Nile Pay features.',
            '✅',
            'green',
            false,
            NOW()
        );

        -- Log email verification
        INSERT INTO public.audit_logs (
            user_id,
            action,
            resource_type,
            resource_id,
            new_values,
            created_at
        ) VALUES (
            NEW.id,
            'EMAIL_VERIFIED',
            'user',
            NEW.id::TEXT,
            jsonb_build_object(
                'email', NEW.email,
                'verified_at', NEW.email_confirmed_at
            ),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for email confirmation
DROP TRIGGER IF EXISTS on_auth_user_email_confirmed ON auth.users;
CREATE TRIGGER on_auth_user_email_confirmed
    AFTER UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_user_email_confirmed();

-- =============================================================================
-- 4. AUTH HELPER FUNCTIONS
-- =============================================================================

-- Function to create password reset request log
CREATE OR REPLACE FUNCTION public.log_password_reset_request(user_email TEXT)
RETURNS VOID AS $$
DECLARE
    user_id UUID;
BEGIN
    -- Get user ID from email
    SELECT id INTO user_id FROM auth.users WHERE email = user_email LIMIT 1;
    
    IF user_id IS NOT NULL THEN
        -- Log password reset request
        INSERT INTO public.audit_logs (
            user_id,
            action,
            resource_type,
            resource_id,
            new_values,
            created_at
        ) VALUES (
            user_id,
            'PASSWORD_RESET_REQUESTED',
            'user',
            user_id::TEXT,
            jsonb_build_object(
                'email', user_email,
                'requested_at', NOW()
            ),
            NOW()
        );

        -- Create notification
        INSERT INTO public.notifications (
            user_id,
            type,
            title,
            message,
            icon,
            color,
            is_read,
            created_at
        ) VALUES (
            user_id,
            'security',
            'Password Reset Requested 🔐',
            'A password reset has been requested for your account. Check your email for instructions.',
            '🔐',
            'yellow',
            false,
            NOW()
        );
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log successful password reset
CREATE OR REPLACE FUNCTION public.log_password_reset_success(user_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Log successful password reset
    INSERT INTO public.audit_logs (
        user_id,
        action,
        resource_type,
        resource_id,
        new_values,
        created_at
    ) VALUES (
        user_id,
        'PASSWORD_RESET_COMPLETED',
        'user',
        user_id::TEXT,
        jsonb_build_object(
            'reset_at', NOW()
        ),
        NOW()
    );

    -- Create notification
    INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        icon,
        color,
        is_read,
        created_at
    ) VALUES (
        user_id,
        'security',
        'Password Changed Successfully ✅',
        'Your password has been changed successfully. If this wasn''t you, please contact support immediately.',
        '✅',
        'green',
        false,
        NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.log_password_reset_request(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_password_reset_success(UUID) TO authenticated;
