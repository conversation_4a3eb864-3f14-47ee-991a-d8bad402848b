"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel";
exports.ids = ["vendor-chunks/@vercel"];
exports.modules = {

/***/ "(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics),\n/* harmony export */   track: () => (/* binding */ track)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,track auto */ // src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/analytics\";\nvar version = \"1.5.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.va) return;\n    window.va = function a(...params) {\n        (window.vaq = window.vaq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction setMode(mode = \"auto\") {\n    if (mode === \"auto\") {\n        window.vam = detectEnvironment();\n        return;\n    }\n    window.vam = mode;\n}\nfunction getMode() {\n    const mode = isBrowser() ? window.vam : detectEnvironment();\n    return mode || \"production\";\n}\nfunction isProduction() {\n    return getMode() === \"production\";\n}\nfunction isDevelopment() {\n    return getMode() === \"development\";\n}\nfunction removeKey(key, { [key]: _, ...rest }) {\n    return rest;\n}\nfunction parseProperties(properties, options) {\n    if (!properties) return void 0;\n    let props = properties;\n    const errorProperties = [];\n    for (const [key, value] of Object.entries(properties)){\n        if (typeof value === \"object\" && value !== null) {\n            if (options.strip) {\n                props = removeKey(key, props);\n            } else {\n                errorProperties.push(key);\n            }\n        }\n    }\n    if (errorProperties.length > 0 && !options.strip) {\n        throw Error(`The following properties are not valid: ${errorProperties.join(\", \")}. Only strings, numbers, booleans, and null are allowed.`);\n    }\n    return props;\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/script.debug.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/insights/script.js`;\n    }\n    return \"/_vercel/insights/script.js\";\n}\n// src/generic.ts\nfunction inject(props = {\n    debug: true\n}) {\n    var _a;\n    if (!isBrowser()) return;\n    setMode(props.mode);\n    initQueue();\n    if (props.beforeSend) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.disableAutoTrack) {\n        script.dataset.disableAutoTrack = \"1\";\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/insights`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    script.onerror = ()=>{\n        const errorMessage = isDevelopment() ? \"Please check if any ad blockers are enabled and try again.\" : \"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.\";\n        console.log(`[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`);\n    };\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    document.head.appendChild(script);\n}\nfunction track(name2, properties, options) {\n    var _a, _b;\n    if (!isBrowser()) {\n        const msg = \"[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment\";\n        if (isProduction()) {\n            console.warn(msg);\n        } else {\n            throw new Error(msg);\n        }\n        return;\n    }\n    if (!properties) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"event\", {\n            name: name2,\n            options\n        });\n        return;\n    }\n    try {\n        const props = parseProperties(properties, {\n            strip: isProduction()\n        });\n        (_b = window.va) == null ? void 0 : _b.call(window, \"event\", {\n            name: name2,\n            data: props,\n            options\n        });\n    } catch (err) {\n        if (err instanceof Error && isDevelopment()) {\n            console.error(err);\n        }\n    }\n}\nfunction pageview({ route, path }) {\n    var _a;\n    (_a = window.va) == null ? void 0 : _a.call(window, \"pageview\", {\n        route,\n        path\n    });\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction Analytics(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        inject({\n            framework: props.framework || \"react\",\n            basePath: props.basePath ?? getBasePath(),\n            ...props.route !== void 0 && {\n                disableAutoTrack: true\n            },\n            ...props\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (props.route && props.path) {\n            pageview({\n                route: props.route,\n                path: props.path\n            });\n        }\n    }, [\n        props.route,\n        props.path\n    ]);\n    return null;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SpeedInsights auto */ // src/nextjs/index.tsx\n\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/speed-insights\";\nvar version = \"1.2.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.si) return;\n    window.si = function a(...params) {\n        (window.siq = window.siq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction isDevelopment() {\n    return detectEnvironment() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[${key}]`);\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[...${key}]`);\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js\";\n    }\n    if (props.dsn) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/speed-insights/script.js`;\n    }\n    return \"/_vercel/speed-insights/script.js\";\n}\n// src/generic.ts\nfunction injectSpeedInsights(props = {}) {\n    var _a;\n    if (!isBrowser() || props.route === null) return null;\n    initQueue();\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n    if (props.beforeSend) {\n        (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.sampleRate) {\n        script.dataset.sampleRate = props.sampleRate.toString();\n    }\n    if (props.route) {\n        script.dataset.route = props.route;\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    script.onerror = ()=>{\n        console.log(`[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`);\n    };\n    document.head.appendChild(script);\n    return {\n        setRoute: (route)=>{\n            script.dataset.route = route ?? void 0;\n        }\n    };\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction SpeedInsights(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    const setScriptRoute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!setScriptRoute.current) {\n            const script = injectSpeedInsights({\n                framework: props.framework ?? \"react\",\n                basePath: props.basePath ?? getBasePath(),\n                ...props\n            });\n            if (script) {\n                setScriptRoute.current = script.setRoute;\n            }\n        } else if (props.route) {\n            setScriptRoute.current(props.route);\n        }\n    }, [\n        props.route\n    ]);\n    return null;\n}\n// src/nextjs/utils.ts\n\nvar useRoute = ()=>{\n    const params = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)() || new URLSearchParams();\n    const path = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    if (!params) {\n        return null;\n    }\n    const finalParams = Object.keys(params).length ? params : Object.fromEntries(searchParams.entries());\n    return computeRoute(path, finalParams);\n};\nfunction getBasePath2() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/nextjs/index.tsx\nfunction SpeedInsightsComponent(props) {\n    const route = useRoute();\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsights, {\n        route,\n        ...props,\n        framework: \"next\",\n        basePath: getBasePath2()\n    });\n}\nfunction SpeedInsights2(props) {\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n        fallback: null\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsightsComponent, {\n        ...props\n    }));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ e0),
/* harmony export */   track: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\banking\nilepay-app\node_modules\@vercel\analytics\dist\react\index.mjs#Analytics`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\banking\nilepay-app\node_modules\@vercel\analytics\dist\react\index.mjs#track`);


/***/ }),

/***/ "(rsc)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SpeedInsights: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\banking\nilepay-app\node_modules\@vercel\speed-insights\dist\next\index.mjs#SpeedInsights`);


/***/ })

};
;