<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Nile Pay Password</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #059669;
            margin-bottom: 10px;
        }
        .flag {
            font-size: 24px;
            margin-left: 10px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #6b7280;
            font-size: 16px;
            margin-bottom: 30px;
        }
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .reset-button:hover {
            transform: translateY(-2px);
        }
        .security-alert {
            background: #fef2f2;
            border: 1px solid #fca5a5;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-alert h4 {
            color: #dc2626;
            margin: 0 0 8px 0;
            font-size: 16px;
        }
        .security-alert p {
            color: #7f1d1d;
            margin: 0;
            font-size: 14px;
        }
        .security-tips {
            background: #f0f9ff;
            border: 1px solid #7dd3fc;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-tips h4 {
            color: #0369a1;
            margin: 0 0 12px 0;
            font-size: 16px;
        }
        .security-tips ul {
            color: #0c4a6e;
            margin: 0;
            padding-left: 20px;
        }
        .security-tips li {
            margin: 6px 0;
            font-size: 14px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .ethiopian-pattern {
            background: linear-gradient(45deg, #fbbf24 25%, transparent 25%), 
                        linear-gradient(-45deg, #fbbf24 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #dc2626 75%), 
                        linear-gradient(-45deg, transparent 75%, #dc2626 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            height: 4px;
            margin: 20px 0;
        }
        .warning-icon {
            font-size: 48px;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                Nile Pay <span class="flag">🇪🇹</span>
            </div>
            <div class="ethiopian-pattern"></div>
        </div>

        <div class="warning-icon">🔐</div>
        
        <h1 class="title">Password Reset Request</h1>
        <p class="subtitle">Secure your Nile Pay account</p>

        <p>Hello,</p>
        
        <p>We received a request to reset the password for your Nile Pay account associated with <strong>{{ .Email }}</strong>.</p>

        <p>If you made this request, click the button below to reset your password:</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ .ConfirmationURL }}" class="reset-button">
                🔑 Reset My Password
            </a>
        </div>

        <div class="security-alert">
            <h4>⚠️ Important Security Information</h4>
            <p><strong>This link will expire in 1 hour</strong> for your security. If you didn't request a password reset, please ignore this email and your password will remain unchanged.</p>
        </div>

        <div class="security-tips">
            <h4>🛡️ Password Security Tips</h4>
            <ul>
                <li>Use a strong, unique password for your Nile Pay account</li>
                <li>Include uppercase, lowercase, numbers, and special characters</li>
                <li>Avoid using personal information like names or birthdays</li>
                <li>Don't reuse passwords from other accounts</li>
                <li>Consider using a password manager</li>
                <li>Enable two-factor authentication for extra security</li>
            </ul>
        </div>

        <p><strong>Didn't request this?</strong></p>
        <p>If you didn't request a password reset, someone may be trying to access your account. Please:</p>
        <ul>
            <li>Ignore this email (your password won't be changed)</li>
            <li>Check your account for any suspicious activity</li>
            <li>Contact our security team if you notice anything unusual</li>
            <li>Consider changing your password as a precaution</li>
        </ul>

        <p>If the button above doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #dc2626; font-family: monospace; background: #f3f4f6; padding: 8px; border-radius: 4px;">{{ .ConfirmationURL }}</p>

        <div class="footer">
            <p><strong>Nile Pay Security Team</strong></p>
            <p>🇪🇹 Protecting Ethiopian Digital Payments</p>
            <p style="margin-top: 16px;">
                Security concerns? Contact us immediately at 
                <a href="mailto:<EMAIL>" style="color: #dc2626;"><EMAIL></a>
            </p>
            <p style="margin-top: 16px;">
                General support: 
                <a href="mailto:<EMAIL>" style="color: #059669;"><EMAIL></a>
            </p>
            <p style="font-size: 12px; margin-top: 16px;">
                This email was sent to {{ .Email }} from your Nile Pay account. 
                If you believe this email was sent in error, please contact our support team.
            </p>
        </div>
    </div>
</body>
</html>
