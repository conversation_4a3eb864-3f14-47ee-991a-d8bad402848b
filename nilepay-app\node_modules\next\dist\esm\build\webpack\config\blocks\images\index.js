import curry from "next/dist/compiled/lodash.curry";
import { nextImageLoaderRegex } from "../../../../webpack-config";
import { loader } from "../../helpers";
import { pipe } from "../../utils";
import { getCustomDocumentImageError } from "./messages";
export const images = curry(async function images(_ctx, config) {
    const fns = [
        loader({
            oneOf: [
                {
                    test: nextImageLoaderRegex,
                    use: {
                        loader: "error-loader",
                        options: {
                            reason: getCustomDocumentImageError()
                        }
                    },
                    issuer: /pages[\\/]_document\./
                }
            ]
        })
    ];
    const fn = pipe(...fns);
    return fn(config);
});

//# sourceMappingURL=index.js.map