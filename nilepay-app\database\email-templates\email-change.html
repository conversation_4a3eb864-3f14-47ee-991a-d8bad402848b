<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm Your New Email Address - Nile Pay</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #059669;
            margin-bottom: 10px;
        }
        .flag {
            font-size: 24px;
            margin-left: 10px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #6b7280;
            font-size: 16px;
            margin-bottom: 30px;
        }
        .confirm-button {
            display: inline-block;
            background: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .confirm-button:hover {
            transform: translateY(-2px);
        }
        .email-change-info {
            background: #f0f9ff;
            border: 1px solid #7dd3fc;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .email-change-info h4 {
            color: #0369a1;
            margin: 0 0 8px 0;
            font-size: 16px;
        }
        .email-change-info p {
            color: #0c4a6e;
            margin: 0;
            font-size: 14px;
        }
        .security-alert {
            background: #fef2f2;
            border: 1px solid #fca5a5;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-alert h4 {
            color: #dc2626;
            margin: 0 0 8px 0;
            font-size: 16px;
        }
        .security-alert p {
            color: #7f1d1d;
            margin: 0;
            font-size: 14px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .ethiopian-pattern {
            background: linear-gradient(45deg, #fbbf24 25%, transparent 25%), 
                        linear-gradient(-45deg, #fbbf24 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #0ea5e9 75%), 
                        linear-gradient(-45deg, transparent 75%, #0ea5e9 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            height: 4px;
            margin: 20px 0;
        }
        .email-icon {
            font-size: 48px;
            text-align: center;
            margin: 20px 0;
        }
        .email-comparison {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .email-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 8px 0;
        }
        .email-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }
        .email-value {
            font-family: monospace;
            background: white;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #d1d5db;
            font-size: 14px;
        }
        .old-email {
            color: #dc2626;
        }
        .new-email {
            color: #059669;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                Nile Pay <span class="flag">🇪🇹</span>
            </div>
            <div class="ethiopian-pattern"></div>
        </div>

        <div class="email-icon">📧</div>
        
        <h1 class="title">Confirm Your New Email Address</h1>
        <p class="subtitle">Secure your Nile Pay account with your updated email</p>

        <p>Hello!</p>
        
        <p>You recently requested to change the email address associated with your Nile Pay account. To complete this change and ensure the security of your account, please confirm your new email address.</p>

        <div class="email-comparison">
            <h4 style="color: #1f2937; margin: 0 0 16px 0;">📋 Email Change Summary</h4>
            
            <div class="email-row">
                <span class="email-label">Current Email:</span>
                <span class="email-value old-email">{{ .Email }}</span>
            </div>
            
            <div class="email-row">
                <span class="email-label">New Email:</span>
                <span class="email-value new-email">{{ .NewEmail }}</span>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ .ConfirmationURL }}" class="confirm-button">
                ✅ Confirm New Email Address
            </a>
        </div>

        <div class="email-change-info">
            <h4>📝 What happens next?</h4>
            <p>After confirming your new email address:</p>
            <ul style="margin: 8px 0 0 20px; padding: 0;">
                <li>Your login email will be updated to the new address</li>
                <li>All future notifications will be sent to your new email</li>
                <li>Your old email address will no longer have access to this account</li>
                <li>You'll receive a confirmation that the change was successful</li>
            </ul>
        </div>

        <div class="security-alert">
            <h4>🔐 Important Security Notice</h4>
            <p><strong>This confirmation link will expire in 24 hours.</strong> If you didn't request this email change, please contact our security team immediately at <a href="mailto:<EMAIL>" style="color: #dc2626;"><EMAIL></a></p>
        </div>

        <p><strong>Why do we require email confirmation?</strong></p>
        <ul style="color: #374151;">
            <li>🛡️ <strong>Account Security:</strong> Prevents unauthorized email changes</li>
            <li>📧 <strong>Email Verification:</strong> Ensures you can receive important notifications</li>
            <li>🔒 <strong>Access Control:</strong> Confirms you own the new email address</li>
            <li>📋 <strong>Audit Trail:</strong> Maintains a record of account changes</li>
        </ul>

        <p><strong>Didn't request this change?</strong></p>
        <p>If you didn't request to change your email address, this could indicate that someone else has access to your account. Please:</p>
        <ul style="color: #374151; font-size: 14px;">
            <li>Do not click the confirmation link</li>
            <li>Change your password immediately</li>
            <li>Contact our security team at <a href="mailto:<EMAIL>" style="color: #dc2626;"><EMAIL></a></li>
            <li>Review your recent account activity</li>
            <li>Enable two-factor authentication if not already active</li>
        </ul>

        <p>If the button above doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #0ea5e9; font-family: monospace; background: #f3f4f6; padding: 8px; border-radius: 4px;">{{ .ConfirmationURL }}</p>

        <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; padding: 16px; margin: 20px 0;">
            <h4 style="color: #166534; margin: 0 0 8px 0;">💡 Account Security Tips</h4>
            <ul style="color: #166534; margin: 0; font-size: 14px; padding-left: 20px;">
                <li>Use a strong, unique password for your Nile Pay account</li>
                <li>Enable two-factor authentication for extra security</li>
                <li>Regularly review your account activity and settings</li>
                <li>Keep your contact information up to date</li>
                <li>Never share your login credentials with anyone</li>
            </ul>
        </div>

        <div class="footer">
            <p><strong>Nile Pay Security Team</strong></p>
            <p>🇪🇹 Protecting Ethiopian Digital Payments</p>
            <p style="margin-top: 16px;">
                Security concerns? Contact us immediately at 
                <a href="mailto:<EMAIL>" style="color: #dc2626;"><EMAIL></a>
            </p>
            <p style="margin-top: 16px;">
                General support: 
                <a href="mailto:<EMAIL>" style="color: #059669;"><EMAIL></a>
            </p>
            <p style="font-size: 12px; margin-top: 16px;">
                This email was sent to {{ .NewEmail }} to confirm your email change request from {{ .Email }}.
            </p>
        </div>
    </div>
</body>
</html>
