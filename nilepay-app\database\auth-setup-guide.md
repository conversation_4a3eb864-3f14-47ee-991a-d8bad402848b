# NilePay Authentication Setup Guide

This guide will help you set up the complete authentication system for NilePay with Supabase, including email templates and auth configuration.

## 🚀 Quick Setup Overview

1. **Apply Auth Migration** - Set up database triggers and functions
2. **Configure Supabase Auth Settings** - Update auth configuration in dashboard
3. **Upload Email Templates** - Set up branded email templates
4. **Test Authentication Flow** - Verify everything works correctly

## 📋 Step 1: Apply Auth Migration

### Run the Auth Configuration SQL

1. Open your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Create a new query
4. Copy and paste the contents of `auth_configuration.sql`
5. Click **Run** to execute the migration

This will set up:
- ✅ User registration triggers
- ✅ Email confirmation handlers
- ✅ Password reset logging
- ✅ Audit trail functions
- ✅ Welcome notifications
- ✅ Default wallet creation

## ⚙️ Step 2: Configure Supabase Auth Settings

### In your Supabase Dashboard:

1. Go to **Authentication** → **Settings**
2. Configure the following settings:

#### Site URL Configuration
```
Site URL: https://your-domain.com
Additional Redirect URLs:
- http://localhost:3000
- https://your-domain.com/auth/callback
- https://your-domain.com/reset-password
```

#### Email Auth Settings
```
✅ Enable email confirmations
✅ Enable email change confirmations  
✅ Enable secure email change
Confirmation URL: https://your-domain.com/auth/confirm
Password Reset URL: https://your-domain.com/reset-password
```

#### Security Settings
```
JWT Expiry: 3600 (1 hour)
Refresh Token Rotation: ✅ Enabled
Session Timeout: 604800 (7 days)
```

#### Rate Limiting
```
Email Rate Limit: 60 per hour
SMS Rate Limit: 60 per hour
```

## 📧 Step 3: Upload Email Templates

### Method 1: Supabase Dashboard (Recommended)

1. Go to **Authentication** → **Email Templates**
2. For each template type, click **Edit**
3. Copy the HTML content from the respective template files:

#### Confirm Signup Template
- Copy content from `email-templates/confirm-signup.html`
- Subject: `Welcome to Nile Pay - Confirm Your Email 🇪🇹`

#### Reset Password Template  
- Copy content from `email-templates/reset-password.html`
- Subject: `Reset Your Nile Pay Password 🔐`

#### Magic Link Template
- Copy content from `email-templates/magic-link.html`
- Subject: `Your Nile Pay Magic Link ✨`

#### Email Change Template
- Copy content from `email-templates/email-change.html`
- Subject: `Confirm Your New Email Address - Nile Pay 📧`

#### Invite User Template
- Copy content from `email-templates/invite-user.html`
- Subject: `You're Invited to Join Nile Pay! 🎉`

### Method 2: Supabase CLI (Advanced)

```bash
# Update email templates via CLI
supabase auth update --email-template confirm-signup --file email-templates/confirm-signup.html
supabase auth update --email-template reset-password --file email-templates/reset-password.html
supabase auth update --email-template magic-link --file email-templates/magic-link.html
supabase auth update --email-template email-change --file email-templates/email-change.html
supabase auth update --email-template invite-user --file email-templates/invite-user.html
```

## 🔧 Step 4: Environment Configuration

### Update your `.env` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# App Configuration
NEXT_PUBLIC_APP_NAME="Nile Pay"
NEXT_PUBLIC_APP_DESCRIPTION="Ethiopian Digital Payment Gateway"
NEXT_PUBLIC_SITE_URL=https://your-domain.com

# Email Configuration (if using custom SMTP)
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password
SMTP_FROM=<EMAIL>
```

## 🧪 Step 5: Test Authentication Flow

### Test User Registration

1. **Sign Up Test:**
   ```bash
   curl -X POST https://your-supabase-url/auth/v1/signup \
     -H "Content-Type: application/json" \
     -H "apikey: your-anon-key" \
     -d '{
       "email": "<EMAIL>",
       "password": "testpassword123",
       "data": {
         "first_name": "Test",
         "last_name": "User",
         "phone": "+************"
       }
     }'
   ```

2. **Check Database:**
   ```sql
   -- Verify user was created in users table
   SELECT * FROM users WHERE email = '<EMAIL>';
   
   -- Check default wallet was created
   SELECT * FROM bank_accounts WHERE user_id = 'user-id-here';
   
   -- Verify welcome notification
   SELECT * FROM notifications WHERE user_id = 'user-id-here';
   ```

### Test Email Confirmation

1. Check your email for the confirmation message
2. Verify the email uses your custom template
3. Click the confirmation link
4. Check that `is_verified` is updated in the database

### Test Password Reset

1. **Request Password Reset:**
   ```bash
   curl -X POST https://your-supabase-url/auth/v1/recover \
     -H "Content-Type: application/json" \
     -H "apikey: your-anon-key" \
     -d '{"email": "<EMAIL>"}'
   ```

2. Check email for reset link with custom template
3. Verify audit log entry was created

## 🔍 Step 6: Verify Setup

### Database Verification Checklist

- [ ] `handle_new_user()` function exists
- [ ] `handle_user_email_confirmed()` function exists  
- [ ] `log_password_reset_request()` function exists
- [ ] `log_password_reset_success()` function exists
- [ ] Triggers are active on `auth.users` table
- [ ] RLS policies are enabled on all tables

### Email Template Verification

- [ ] Confirm signup email displays correctly
- [ ] Password reset email displays correctly
- [ ] Magic link email displays correctly
- [ ] Email change confirmation displays correctly
- [ ] Invite user email displays correctly
- [ ] All templates show Ethiopian branding (🇪🇹 flag, colors)
- [ ] Links work correctly in all templates

### Functional Testing

- [ ] User registration creates profile, settings, and default wallet
- [ ] Email confirmation updates verification status
- [ ] Password reset creates audit logs and notifications
- [ ] Welcome notification is created for new users
- [ ] All auth redirects work correctly

## 🚨 Troubleshooting

### Common Issues

1. **Email Templates Not Updating**
   - Clear browser cache
   - Check template syntax for errors
   - Verify you're editing the correct template type

2. **Triggers Not Firing**
   - Check function permissions
   - Verify trigger is enabled: `SELECT * FROM pg_trigger WHERE tgname LIKE '%auth%';`
   - Check Supabase logs for errors

3. **RLS Policy Errors**
   - Ensure service role key is used for admin operations
   - Check policy syntax in SQL editor
   - Verify user context in policies

4. **Email Delivery Issues**
   - Check Supabase email quota
   - Verify SMTP configuration if using custom provider
   - Check spam folders

### Debug Queries

```sql
-- Check if triggers exist
SELECT * FROM pg_trigger WHERE tgname IN ('on_auth_user_created', 'on_auth_user_email_confirmed');

-- Check recent auth events
SELECT * FROM auth.audit_log_entries ORDER BY created_at DESC LIMIT 10;

-- Verify user creation flow
SELECT u.email, u.is_verified, ba.account_name, n.title 
FROM users u
LEFT JOIN bank_accounts ba ON u.id = ba.user_id
LEFT JOIN notifications n ON u.id = n.user_id
WHERE u.email = '<EMAIL>';
```

## 📞 Support

If you encounter issues:

1. **Check Supabase Logs:** Dashboard → Logs → Auth
2. **Review Database Logs:** Dashboard → Logs → Database  
3. **Test with Supabase CLI:** `supabase auth test`
4. **Contact Support:** Create an issue with detailed error logs

## 🎉 Success!

Once everything is set up correctly, your NilePay authentication system will provide:

- ✅ Seamless user registration with Ethiopian banking setup
- ✅ Beautiful, branded email templates
- ✅ Comprehensive audit logging
- ✅ Automatic wallet and settings creation
- ✅ Security notifications and alerts
- ✅ Full Ethiopian banking integration

Your users will have a smooth, secure, and culturally relevant authentication experience! 🇪🇹
