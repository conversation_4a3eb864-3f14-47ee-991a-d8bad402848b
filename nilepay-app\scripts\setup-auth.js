#!/usr/bin/env node

/**
 * NilePay Authentication Setup Script
 * 
 * This script configures Supabase authentication settings and uploads email templates
 * for the NilePay Ethiopian banking application.
 * 
 * Usage: node scripts/setup-auth.js
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Supabase configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

console.log('🚀 Setting up NilePay Authentication...\n');

/**
 * Read email template file
 */
function readTemplate(templateName) {
  const templatePath = path.join(__dirname, '..', 'database', 'email-templates', `${templateName}.html`);
  
  if (!fs.existsSync(templatePath)) {
    console.error(`❌ Template file not found: ${templatePath}`);
    return null;
  }
  
  return fs.readFileSync(templatePath, 'utf8');
}

/**
 * Update Supabase auth configuration
 */
async function updateAuthConfig() {
  console.log('⚙️  Updating Supabase auth configuration...');
  
  const authConfig = {
    SITE_URL: SITE_URL,
    URI_ALLOW_LIST: [
      'http://localhost:3000',
      'http://localhost:3000/**',
      SITE_URL,
      `${SITE_URL}/**`,
      `${SITE_URL}/auth/callback`,
      `${SITE_URL}/reset-password`
    ].join(','),
    JWT_EXP: 3600, // 1 hour
    REFRESH_TOKEN_ROTATION_ENABLED: true,
    SECURITY_REFRESH_TOKEN_REUSE_INTERVAL: 10,
    ENABLE_SIGNUP: true,
    ENABLE_EMAIL_CONFIRMATIONS: true,
    ENABLE_EMAIL_CHANGE_CONFIRMATIONS: true,
    ENABLE_SECURE_EMAIL_CHANGE: true,
    RATE_LIMIT_EMAIL_SENT: 60, // per hour
    RATE_LIMIT_SMS_SENT: 60, // per hour
    MAILER_AUTOCONFIRM: false,
    MAILER_SECURE_EMAIL_CHANGE_ENABLED: true,
    MAILER_OTP_EXP: 86400, // 24 hours
    MAILER_URLPATHS_CONFIRMATION: '/auth/confirm',
    MAILER_URLPATHS_INVITE: '/auth/confirm',
    MAILER_URLPATHS_RECOVERY: '/reset-password',
    MAILER_URLPATHS_EMAIL_CHANGE: '/auth/confirm'
  };

  try {
    const response = await fetch(`${SUPABASE_URL}/auth/v1/settings`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'Content-Type': 'application/json',
        'apikey': SUPABASE_SERVICE_KEY
      },
      body: JSON.stringify(authConfig)
    });

    if (response.ok) {
      console.log('✅ Auth configuration updated successfully');
    } else {
      const error = await response.text();
      console.error('❌ Failed to update auth configuration:', error);
    }
  } catch (error) {
    console.error('❌ Error updating auth configuration:', error.message);
  }
}

/**
 * Upload email template to Supabase
 */
async function uploadEmailTemplate(templateType, templateContent, subject) {
  console.log(`📧 Uploading ${templateType} email template...`);
  
  const templateData = {
    template: templateContent,
    subject: subject
  };

  try {
    const response = await fetch(`${SUPABASE_URL}/auth/v1/settings/email-templates/${templateType}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'Content-Type': 'application/json',
        'apikey': SUPABASE_SERVICE_KEY
      },
      body: JSON.stringify(templateData)
    });

    if (response.ok) {
      console.log(`✅ ${templateType} template uploaded successfully`);
    } else {
      const error = await response.text();
      console.error(`❌ Failed to upload ${templateType} template:`, error);
    }
  } catch (error) {
    console.error(`❌ Error uploading ${templateType} template:`, error.message);
  }
}

/**
 * Setup all email templates
 */
async function setupEmailTemplates() {
  console.log('\n📧 Setting up email templates...');
  
  const templates = [
    {
      type: 'confirmation',
      file: 'confirm-signup',
      subject: 'Welcome to Nile Pay - Confirm Your Email 🇪🇹'
    },
    {
      type: 'recovery',
      file: 'reset-password',
      subject: 'Reset Your Nile Pay Password 🔐'
    },
    {
      type: 'magic_link',
      file: 'magic-link',
      subject: 'Your Nile Pay Magic Link ✨'
    },
    {
      type: 'email_change',
      file: 'email-change',
      subject: 'Confirm Your New Email Address - Nile Pay 📧'
    },
    {
      type: 'invite',
      file: 'invite-user',
      subject: 'You\'re Invited to Join Nile Pay! 🎉'
    }
  ];

  for (const template of templates) {
    const content = readTemplate(template.file);
    if (content) {
      await uploadEmailTemplate(template.type, content, template.subject);
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

/**
 * Verify setup by testing auth endpoints
 */
async function verifySetup() {
  console.log('\n🔍 Verifying authentication setup...');
  
  try {
    // Test auth settings endpoint
    const settingsResponse = await fetch(`${SUPABASE_URL}/auth/v1/settings`, {
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'apikey': SUPABASE_SERVICE_KEY
      }
    });

    if (settingsResponse.ok) {
      const settings = await settingsResponse.json();
      console.log('✅ Auth settings accessible');
      console.log(`   Site URL: ${settings.external_url || 'Not set'}`);
      console.log(`   Email confirmations: ${settings.mailer_autoconfirm ? 'Disabled' : 'Enabled'}`);
    } else {
      console.error('❌ Failed to fetch auth settings');
    }

    // Test database connection
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
    
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (!error) {
      console.log('✅ Database connection successful');
    } else {
      console.error('❌ Database connection failed:', error.message);
    }

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

/**
 * Main setup function
 */
async function main() {
  try {
    console.log('🇪🇹 NilePay Authentication Setup');
    console.log('================================\n');
    
    // Step 1: Update auth configuration
    await updateAuthConfig();
    
    // Step 2: Setup email templates
    await setupEmailTemplates();
    
    // Step 3: Verify setup
    await verifySetup();
    
    console.log('\n🎉 Authentication setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Run the auth_configuration.sql migration in Supabase SQL Editor');
    console.log('2. Test user registration and email confirmation');
    console.log('3. Verify email templates in Supabase Dashboard > Auth > Email Templates');
    console.log('\n📚 See auth-setup-guide.md for detailed instructions');
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  main();
}

module.exports = {
  updateAuthConfig,
  setupEmailTemplates,
  verifySetup
};
