"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    MAGIC_IDENTIFIER_REGEX: null,
    decodeMagicIdentifier: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    MAGIC_IDENTIFIER_REGEX: function() {
        return MAGIC_IDENTIFIER_REGEX;
    },
    decodeMagicIdentifier: function() {
        return decodeMagicIdentifier;
    }
});
function decodeHex(hexStr) {
    if (hexStr.trim() === "") {
        throw new Error("can't decode empty hex");
    }
    const num = parseInt(hexStr, 16);
    if (isNaN(num)) {
        throw new Error("invalid hex: `" + hexStr + "`");
    }
    return String.fromCodePoint(num);
}
var Mode;
const DECODE_REGEX = /^__TURBOPACK__([a-zA-Z0-9_$]+)__$/;
function decodeMagicIdentifier(identifier) {
    const matches = identifier.match(DECODE_REGEX);
    if (!matches) {
        return identifier;
    }
    const inner = matches[1];
    let output = "";
    let mode = 0;
    let buffer = "";
    for(let i = 0; i < inner.length; i++){
        const char = inner[i];
        if (mode === 0) {
            if (char === "_") {
                mode = 1;
            } else if (char === "$") {
                mode = 2;
            } else {
                output += char;
            }
        } else if (mode === 1) {
            if (char === "_") {
                output += " ";
                mode = 0;
            } else if (char === "$") {
                output += "_";
                mode = 2;
            } else {
                output += char;
                mode = 0;
            }
        } else if (mode === 2) {
            if (buffer.length === 2) {
                output += decodeHex(buffer);
                buffer = "";
            }
            if (char === "_") {
                if (buffer !== "") {
                    throw new Error("invalid hex: `" + buffer + "`");
                }
                mode = 3;
            } else if (char === "$") {
                if (buffer !== "") {
                    throw new Error("invalid hex: `" + buffer + "`");
                }
                mode = 0;
            } else {
                buffer += char;
            }
        } else if (mode === 3) {
            if (char === "_") {
                throw new Error("invalid hex: `" + (buffer + char) + "`");
            } else if (char === "$") {
                output += decodeHex(buffer);
                buffer = "";
                mode = 0;
            } else {
                buffer += char;
            }
        }
    }
    return output;
}
const MAGIC_IDENTIFIER_REGEX = /__TURBOPACK__[a-zA-Z0-9_$]+__/g;

//# sourceMappingURL=magic-identifier.js.map