<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Nile Pay - Confirm Your Email</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #059669;
            margin-bottom: 10px;
        }
        .flag {
            font-size: 24px;
            margin-left: 10px;
        }
        .welcome-title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #6b7280;
            font-size: 16px;
            margin-bottom: 30px;
        }
        .confirm-button {
            display: inline-block;
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .confirm-button:hover {
            transform: translateY(-2px);
        }
        .security-note {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-note h4 {
            color: #92400e;
            margin: 0 0 8px 0;
            font-size: 14px;
        }
        .security-note p {
            color: #92400e;
            margin: 0;
            font-size: 14px;
        }
        .features {
            margin: 30px 0;
        }
        .feature {
            display: flex;
            align-items: center;
            margin: 12px 0;
            padding: 12px;
            background: #f0fdf4;
            border-radius: 8px;
        }
        .feature-icon {
            font-size: 20px;
            margin-right: 12px;
        }
        .feature-text {
            color: #166534;
            font-weight: 500;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .ethiopian-pattern {
            background: linear-gradient(45deg, #fbbf24 25%, transparent 25%), 
                        linear-gradient(-45deg, #fbbf24 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #10b981 75%), 
                        linear-gradient(-45deg, transparent 75%, #10b981 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            height: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                Nile Pay <span class="flag">🇪🇹</span>
            </div>
            <div class="ethiopian-pattern"></div>
        </div>

        <h1 class="welcome-title">Welcome to Nile Pay!</h1>
        <p class="subtitle">Ethiopia's Premier Digital Payment Gateway</p>

        <p>Hello and welcome to the future of Ethiopian banking! 🎉</p>
        
        <p>Thank you for joining Nile Pay, Ethiopia's most trusted digital payment platform. To complete your registration and start enjoying secure banking services, please confirm your email address.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ .ConfirmationURL }}" class="confirm-button">
                ✅ Confirm Your Email Address
            </a>
        </div>

        <div class="security-note">
            <h4>🔐 Security Notice</h4>
            <p>This confirmation link will expire in 24 hours for your security. If you didn't create a Nile Pay account, please ignore this email.</p>
        </div>

        <div class="features">
            <h3 style="color: #1f2937; margin-bottom: 16px;">What you can do with Nile Pay:</h3>
            
            <div class="feature">
                <span class="feature-icon">🏦</span>
                <span class="feature-text">Connect multiple Ethiopian bank accounts</span>
            </div>
            
            <div class="feature">
                <span class="feature-icon">💸</span>
                <span class="feature-text">Send money instantly across Ethiopia</span>
            </div>
            
            <div class="feature">
                <span class="feature-icon">📱</span>
                <span class="feature-text">Mobile money transfers (M-Birr, HelloCash, TeleBirr)</span>
            </div>
            
            <div class="feature">
                <span class="feature-icon">⚡</span>
                <span class="feature-text">Pay bills (EEPCO, Ethio Telecom, Water)</span>
            </div>
            
            <div class="feature">
                <span class="feature-icon">📊</span>
                <span class="feature-text">Track expenses and manage budgets</span>
            </div>
            
            <div class="feature">
                <span class="feature-icon">🔒</span>
                <span class="feature-text">Bank-level security with Ethiopian compliance</span>
            </div>
        </div>

        <p>If the button above doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #059669; font-family: monospace; background: #f3f4f6; padding: 8px; border-radius: 4px;">{{ .ConfirmationURL }}</p>

        <div class="footer">
            <p><strong>Nile Pay</strong> - Empowering Ethiopian Digital Payments</p>
            <p>🇪🇹 Made with ❤️ for Ethiopia</p>
            <p style="margin-top: 16px;">
                Need help? Contact our support team at 
                <a href="mailto:<EMAIL>" style="color: #059669;"><EMAIL></a>
            </p>
            <p style="font-size: 12px; margin-top: 16px;">
                This email was sent to {{ .Email }}. If you didn't sign up for Nile Pay, you can safely ignore this email.
            </p>
        </div>
    </div>
</body>
</html>
